import lustre/element.{type Element}
import lustre/element/html
import lustre/event
import lustre/attribute

// TYPES -----------------------------------------------------------------------

pub type ButtonVariant {
  Primary
  Secondary
  Danger
  Success
}

pub type ButtonSize {
  Small
  Medium
  Large
}

pub type ButtonConfig(msg) {
  ButtonConfig(
    text: String,
    on_click: msg,
    variant: ButtonVariant,
    size: ButtonSize,
    disabled: Bool,
    class: String,
  )
}

// API FUNCTIONS ---------------------------------------------------------------

/// Create a button with default configuration
pub fn button(text: String, on_click: msg) -> Element(msg) {
  ButtonConfig(
    text: text,
    on_click: on_click,
    variant: Primary,
    size: Medium,
    disabled: False,
    class: "",
  )
  |> render()
}

/// Create a primary button
pub fn primary(text: String, on_click: msg) -> Element(msg) {
  ButtonConfig(
    text: text,
    on_click: on_click,
    variant: Primary,
    size: Medium,
    disabled: False,
    class: "",
  )
  |> render()
}

/// Create a secondary button
pub fn secondary(text: String, on_click: msg) -> Element(msg) {
  ButtonConfig(
    text: text,
    on_click: on_click,
    variant: Secondary,
    size: Medium,
    disabled: False,
    class: "",
  )
  |> render()
}

/// Create a danger button
pub fn danger(text: String, on_click: msg) -> Element(msg) {
  ButtonConfig(
    text: text,
    on_click: on_click,
    variant: Danger,
    size: Medium,
    disabled: False,
    class: "",
  )
  |> render()
}

/// Create a success button
pub fn success(text: String, on_click: msg) -> Element(msg) {
  ButtonConfig(
    text: text,
    on_click: on_click,
    variant: Success,
    size: Medium,
    disabled: False,
    class: "",
  )
  |> render()
}

/// Create a small button
pub fn small(config: ButtonConfig(msg)) -> ButtonConfig(msg) {
  ButtonConfig(..config, size: Small)
}

/// Create a large button
pub fn large(config: ButtonConfig(msg)) -> ButtonConfig(msg) {
  ButtonConfig(..config, size: Large)
}

/// Disable a button
pub fn disabled(config: ButtonConfig(msg)) -> ButtonConfig(msg) {
  ButtonConfig(..config, disabled: True)
}

/// Add custom CSS class to button
pub fn with_class(config: ButtonConfig(msg), class: String) -> ButtonConfig(msg) {
  ButtonConfig(..config, class: class)
}

/// Create a button with full configuration
pub fn with_config(config: ButtonConfig(msg)) -> Element(msg) {
  render(config)
}

// INTERNAL FUNCTIONS ----------------------------------------------------------

fn render(config: ButtonConfig(msg)) -> Element(msg) {
  let base_classes = "btn " <> variant_class(config.variant) <> " " <> size_class(config.size)
  let classes = case config.class {
    "" -> base_classes
    custom -> base_classes <> " " <> custom
  }
  
  let attributes = [
    attribute.class(classes),
    ..case config.disabled {
      True -> [attribute.disabled(True)]
      False -> [event.on_click(config.on_click)]
    }
  ]
  
  html.button(attributes, [element.text(config.text)])
}

fn variant_class(variant: ButtonVariant) -> String {
  case variant {
    Primary -> "btn-primary"
    Secondary -> "btn-secondary"
    Danger -> "btn-danger"
    Success -> "btn-success"
  }
}

fn size_class(size: ButtonSize) -> String {
  case size {
    Small -> "btn-sm"
    Medium -> "btn-md"
    Large -> "btn-lg"
  }
}
