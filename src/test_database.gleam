import database
import gleam/io
import gleam/int

pub fn main() {
  io.println("Testing database functionality...")
  
  case database.init() {
    Ok(db) -> {
      io.println("✅ Database initialized successfully")
      
      // Test getting initial counter value
      case database.get_counter(db) {
        Ok(count) -> {
          io.println("✅ Initial counter value: " <> int.to_string(count))
          
          // Test incrementing
          case database.increment_counter(db) {
            Ok(new_count) -> {
              io.println("✅ Incremented counter to: " <> int.to_string(new_count))
              
              // Test decrementing
              case database.decrement_counter(db) {
                Ok(final_count) -> {
                  io.println("✅ Decremented counter to: " <> int.to_string(final_count))
                  
                  // Test reset
                  case database.reset_counter(db) {
                    Ok(reset_count) -> {
                      io.println("✅ Reset counter to: " <> int.to_string(reset_count))
                      
                      // Close database
                      case database.close(db) {
                        Ok(_) -> io.println("✅ Database closed successfully")
                        Error(err) -> {
                          case err {
                            database.ConnectionError(msg) -> io.println("❌ Connection error: " <> msg)
                            database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
                          }
                        }
                      }
                    }
                    Error(err) -> {
                      case err {
                        database.ConnectionError(msg) -> io.println("❌ Connection error: " <> msg)
                        database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
                      }
                    }
                  }
                }
                Error(err) -> {
                  case err {
                    database.ConnectionError(msg) -> io.println("❌ Connection error: " <> msg)
                    database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
                  }
                }
              }
            }
            Error(err) -> {
              case err {
                database.ConnectionError(msg) -> io.println("❌ Connection error: " <> msg)
                database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
              }
            }
          }
        }
        Error(err) -> {
          case err {
            database.ConnectionError(msg) -> io.println("❌ Connection error: " <> msg)
            database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
          }
        }
      }
    }
    Error(err) -> {
      case err {
        database.ConnectionError(msg) -> io.println("❌ Connection error: " <> msg)
        database.QueryError(msg) -> io.println("❌ Query error: " <> msg)
      }
    }
  }
}
