import gleam/json
import gleam/dynamic/decode
import wisp.{type Request, type Response}
import gleam/http.{Get, Post}
import database.{type Database}

// HANDLERS --------------------------------------------------------------------

pub fn handle_request(req: Request, db: Database) -> Response {
  // Add CORS headers to all responses
  let response = case req.method, wisp.path_segments(req) {
    Get, ["api", "count"] -> get_count(db)
    Post, ["api", "count"] -> update_count(req, db)
    _, _ -> wisp.not_found()
  }

  // Add CORS headers to response
  response
  |> wisp.set_header("Access-Control-Allow-Origin", "*")
  |> wisp.set_header("Access-Control-Allow-Methods", "GET, POST, OPTIONS")
  |> wisp.set_header("Access-Control-Allow-Headers", "Content-Type")
}

fn get_count(db: Database) -> Response {
  case database.get_counter(db) {
    Ok(count) -> {
      let json_response = json.object([
        #("count", json.int(count))
      ])

      json_response
      |> json.to_string_tree
      |> wisp.json_response(200)
    }
    Error(database.ConnectionError(msg)) -> {
      let error_response = json.object([
        #("error", json.string("Database connection error: " <> msg))
      ])

      error_response
      |> json.to_string_tree
      |> wisp.json_response(500)
    }
    Error(database.QueryError(msg)) -> {
      let error_response = json.object([
        #("error", json.string("Database query error: " <> msg))
      ])

      error_response
      |> json.to_string_tree
      |> wisp.json_response(500)
    }
  }
}

fn update_count(req: Request, db: Database) -> Response {
  use json_data <- wisp.require_json(req)

  // Parse the action from the JSON request
  let action_decoder = {
    use action <- decode.field("action", decode.string)
    decode.success(action)
  }
  let action_result = decode.run(json_data, action_decoder)

  case action_result {
    Ok(action) -> {
      let db_result = case action {
        "increment" -> database.increment_counter(db)
        "decrement" -> database.decrement_counter(db)
        "reset" -> database.reset_counter(db)
        _ -> Error(database.QueryError("Invalid action: " <> action))
      }

      case db_result {
        Ok(new_count) -> {
          let json_response = json.object([
            #("count", json.int(new_count))
          ])

          json_response
          |> json.to_string_tree
          |> wisp.json_response(200)
        }
        Error(database.ConnectionError(msg)) -> {
          let error_response = json.object([
            #("error", json.string("Database connection error: " <> msg))
          ])

          error_response
          |> json.to_string_tree
          |> wisp.json_response(500)
        }
        Error(database.QueryError(msg)) -> {
          let error_response = json.object([
            #("error", json.string("Database query error: " <> msg))
          ])

          error_response
          |> json.to_string_tree
          |> wisp.json_response(500)
        }
      }
    }
    Error(_) -> {
      let error_response = json.object([
        #("error", json.string("Invalid JSON: missing 'action' field"))
      ])

      error_response
      |> json.to_string_tree
      |> wisp.json_response(400)
    }
  }
}

// SERVER SETUP ----------------------------------------------------------------

pub fn start_server() -> Result(Nil, String) {
  case database.init() {
    Ok(_db) -> {
      // For now, just return success
      // In a real app, you'd use mist to start the server with the database
      // let assert Ok(_) = mist.new(fn(req) { handle_request(req, db) })
      //   |> mist.port(8080)
      //   |> mist.start_http
      Ok(Nil)
    }
    Error(database.ConnectionError(msg)) -> Error("Failed to initialize database: " <> msg)
    Error(database.QueryError(msg)) -> Error("Database setup error: " <> msg)
  }
}
